#!/usr/bin/env python3
"""
筛查工具配置系统演示脚本（简化版本）

演示修改后的筛查工具系统：
1. 注销了操作者依赖性因子和年龄相关性能调整
2. 实现了风险问卷和其他工具
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.modules.screening import (
    ScreeningToolFactory, ScreeningToolType, 
    ScreeningToolConfigManager, ScreeningToolConfigTemplates
)
from src.core.enums import DiseaseState, Gender
from src.core.individual import Individual
from src.modules.disease import RiskFactorProfile, RiskFactor, RiskFactorType


def demo_simplified_screening_tools():
    """演示简化后的筛查工具功能"""
    print("=== 筛查工具配置系统演示（简化版本） ===\n")
    
    # 1. 展示所有可用的工具类型
    print("1. 可用的筛查工具类型：")
    available_tools = ScreeningToolFactory.get_available_tools()
    for tool_type in available_tools:
        print(f"   - {tool_type.display_name} ({tool_type.value})")
    print()
    
    # 2. 创建各种筛查工具实例
    print("2. 创建筛查工具实例：")
    tools = {}
    for tool_type in available_tools:
        try:
            tool = ScreeningToolFactory.create_tool(tool_type)
            tools[tool_type] = tool
            print(f"   ✓ {tool_type.display_name} 创建成功")
        except Exception as e:
            print(f"   ✗ {tool_type.display_name} 创建失败: {e}")
    print()
    
    # 3. 展示工具基本信息（注意操作者依赖性已被注销）
    print("3. 工具基本信息（已简化）：")
    for tool_type, tool in tools.items():
        info = tool.get_tool_info()
        print(f"   {tool_type.display_name}:")
        print(f"     - 侵入性: {info['invasiveness']}")
        print(f"     - 特异性: {info['specificity']:.2f}")
        print(f"     - 周转时间: {info['turnaround_time_days']} 天")
        # 注意：operator_skill 仍然显示，但相关计算逻辑已被注销
        print(f"     - 操作技能要求: {info.get('operator_skill', 'N/A')} (仅显示，不参与计算)")
        print()
    
    # 4. 演示风险问卷工具的特殊功能
    print("4. 风险评估问卷工具演示：")
    if ScreeningToolType.RISK_QUESTIONNAIRE in tools:
        risk_tool = tools[ScreeningToolType.RISK_QUESTIONNAIRE]
        
        # 创建一个有风险因素的个体
        individual = Individual(birth_year=1970, gender=Gender.MALE, individual_id="demo_001")

        # 添加风险因素
        individual.set_risk_factor(RiskFactorType.FAMILY_HISTORY, True, source="demo")
        individual.set_risk_factor(RiskFactorType.BMI, 28.5, source="demo")
        
        # 检查个体是否适合风险评估
        eligible = risk_tool.validate_individual_eligibility(individual)
        print(f"   个体适合风险评估: {eligible}")
        
        if eligible:
            # 计算检测概率（基于风险评分）
            detection_prob = risk_tool.calculate_detection_probability(individual)
            print(f"   风险评分: {detection_prob:.3f}")
            
            # 执行筛查
            try:
                result = risk_tool.perform_screening(individual)
                print(f"   筛查结果: {result}")
            except Exception as e:
                print(f"   筛查执行失败: {e}")
    print()
    
    # 5. 演示其他工具的可配置性
    print("5. 其他工具配置演示：")
    if ScreeningToolType.OTHER in tools:
        other_tool = tools[ScreeningToolType.OTHER]
        tool_info = other_tool.get_tool_info()
        
        print(f"   工具名称: {tool_info.get('tool_name', 'N/A')}")
        print(f"   工具描述: {tool_info.get('tool_description', 'N/A')}")
        print(f"   检测机制: {tool_info.get('detection_mechanism', {}).get('type', 'N/A')}")
        print(f"   是否为通用实现: {tool_info.get('is_generic_implementation', False)}")
        print(f"   是否可定制: {tool_info.get('customizable', False)}")
    print()
    
    # 6. 展示配置模板功能
    print("6. 配置模板功能：")
    templates = ScreeningToolConfigTemplates.get_available_templates()
    print("   可用模板：")
    for template_key, template_name in templates.items():
        print(f"     - {template_key}: {template_name}")
    
    # 创建风险问卷模板示例
    print("\n   风险问卷模板示例：")
    risk_template = ScreeningToolConfigTemplates.create_risk_questionnaire_template()
    risk_config = risk_template["screening_tool"]
    print(f"     - 工具名称: {risk_config['name']}")
    print(f"     - 特异性: {risk_config['performance']['specificity']}")
    print(f"     - 风险阈值: {risk_config['performance']['risk_thresholds']}")
    print()
    
    # 7. 展示简化的性能计算（无年龄和操作者调整）
    print("7. 简化的性能计算演示：")
    individual = Individual(birth_year=1980, gender=Gender.FEMALE, individual_id="demo_002")
    individual.current_disease_state = DiseaseState.HIGH_RISK_ADENOMA
    
    for tool_type, tool in tools.items():
        if tool_type in [ScreeningToolType.FIT, ScreeningToolType.COLONOSCOPY]:
            detection_prob = tool.calculate_detection_probability(
                individual,
                individual.current_disease_state
            )
            specificity = tool.calculate_specificity(individual)
            print(f"   {tool_type.display_name}:")
            print(f"     - 检测概率: {detection_prob:.3f} (已简化，无年龄/操作者调整)")
            print(f"     - 特异性: {specificity:.3f}")
    print()
    
    print("=== 演示完成 ===")
    print("\n主要修改总结：")
    print("1. ✓ 注销了操作者依赖性因子相关代码")
    print("2. ✓ 注销了年龄相关性能调整相关代码") 
    print("3. ✓ 实现了风险评估问卷工具")
    print("4. ✓ 实现了通用的其他工具")
    print("5. ✓ 简化了配置文件，减少了实施难度")
    print("6. ✓ 保留了代码结构，便于后期重新启用功能")


if __name__ == "__main__":
    demo_simplified_screening_tools()
