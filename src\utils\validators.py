"""
数据验证和错误处理模块

提供各种数据验证函数和自定义异常类，
确保数据的完整性和一致性。
"""

import re
from datetime import datetime, date
from typing import Any, List, Optional, Union, Type, Dict
from enum import Enum

from ..core.enums import DiseaseState, Gender, PathwayType, CancerStage


class ValidationError(Exception):
    """数据验证错误基类"""
    
    def __init__(self, message: str, field_name: Optional[str] = None, value: Any = None):
        self.message = message
        self.field_name = field_name
        self.value = value
        super().__init__(self.message)
    
    def __str__(self) -> str:
        if self.field_name:
            return f"验证错误 [{self.field_name}]: {self.message}"
        return f"验证错误: {self.message}"


class AgeValidationError(ValidationError):
    """年龄验证错误"""
    pass


class GenderValidationError(ValidationError):
    """性别验证错误"""
    pass


class DateValidationError(ValidationError):
    """日期验证错误"""
    pass


class StateTransitionError(ValidationError):
    """状态转换验证错误"""
    pass


class ParameterValidationError(ValidationError):
    """参数验证错误"""
    pass


class RiskFactorValidationError(ValidationError):
    """风险因素验证错误"""
    pass


def validate_age(age: Union[int, float], field_name: str = "age") -> float:
    """
    验证年龄
    
    Args:
        age: 年龄值
        field_name: 字段名称
        
    Returns:
        验证后的年龄值
        
    Raises:
        AgeValidationError: 年龄无效时
    """
    try:
        age_float = float(age)
    except (TypeError, ValueError):
        raise AgeValidationError(
            f"年龄必须是数字类型，得到: {type(age).__name__}",
            field_name,
            age
        )
    
    if age_float < 0:
        raise AgeValidationError(
            f"年龄不能为负数，得到: {age_float}",
            field_name,
            age_float
        )
    
    if age_float > 100:
        raise AgeValidationError(
            f"年龄不能超过100岁，得到: {age_float}",
            field_name,
            age_float
        )
    
    return age_float


def validate_birth_year(birth_year: int, field_name: str = "birth_year") -> int:
    """
    验证出生年份
    
    Args:
        birth_year: 出生年份
        field_name: 字段名称
        
    Returns:
        验证后的出生年份
        
    Raises:
        DateValidationError: 出生年份无效时
    """
    if not isinstance(birth_year, (int, float)) or isinstance(birth_year, bool):
        raise DateValidationError(
            f"出生年份必须是整数类型，得到: {type(birth_year).__name__}",
            field_name,
            birth_year
        )

    try:
        year_int = int(birth_year)
        if year_int != birth_year:  # 检查是否为浮点数
            raise DateValidationError(
                f"出生年份必须是整数类型，得到: {type(birth_year).__name__}",
                field_name,
                birth_year
            )
    except (TypeError, ValueError):
        raise DateValidationError(
            f"出生年份必须是整数类型，得到: {type(birth_year).__name__}",
            field_name,
            birth_year
        )
    
    current_year = datetime.now().year
    
    if year_int < 2020:
        raise DateValidationError(
            f"出生年份不能早于2020年，得到: {year_int}",
            field_name,
            year_int
        )
    
    if year_int > current_year:
        raise DateValidationError(
            f"出生年份不能晚于当前年份({current_year})，得到: {year_int}",
            field_name,
            year_int
        )
    
    return year_int


def validate_gender(gender: Union[str, Gender], field_name: str = "gender") -> Gender:
    """
    验证性别
    
    Args:
        gender: 性别值
        field_name: 字段名称
        
    Returns:
        验证后的性别枚举
        
    Raises:
        GenderValidationError: 性别无效时
    """
    if isinstance(gender, Gender):
        return gender
    
    if isinstance(gender, str):
        gender_lower = gender.lower()
        for gender_enum in Gender:
            if gender_enum.value.lower() == gender_lower:
                return gender_enum
        
        raise GenderValidationError(
            f"无效的性别值，期望: {[g.value for g in Gender]}，得到: {gender}",
            field_name,
            gender
        )
    
    raise GenderValidationError(
        f"性别必须是字符串或Gender枚举，得到: {type(gender).__name__}",
        field_name,
        gender
    )


def validate_disease_state(
    disease_state: Union[str, DiseaseState], 
    field_name: str = "disease_state"
) -> DiseaseState:
    """
    验证疾病状态
    
    Args:
        disease_state: 疾病状态值
        field_name: 字段名称
        
    Returns:
        验证后的疾病状态枚举
        
    Raises:
        ValidationError: 疾病状态无效时
    """
    if isinstance(disease_state, DiseaseState):
        return disease_state
    
    if isinstance(disease_state, str):
        for state_enum in DiseaseState:
            if state_enum.value == disease_state:
                return state_enum
        
        raise ValidationError(
            f"无效的疾病状态，期望: {[s.value for s in DiseaseState]}，得到: {disease_state}",
            field_name,
            disease_state
        )
    
    raise ValidationError(
        f"疾病状态必须是字符串或DiseaseState枚举，得到: {type(disease_state).__name__}",
        field_name,
        disease_state
    )


def validate_pathway_type(
    pathway_type: Union[str, PathwayType, None], 
    field_name: str = "pathway_type"
) -> Optional[PathwayType]:
    """
    验证疾病通路类型
    
    Args:
        pathway_type: 通路类型值
        field_name: 字段名称
        
    Returns:
        验证后的通路类型枚举或None
        
    Raises:
        ValidationError: 通路类型无效时
    """
    if pathway_type is None:
        return None
    
    if isinstance(pathway_type, PathwayType):
        return pathway_type
    
    if isinstance(pathway_type, str):
        for pathway_enum in PathwayType:
            if pathway_enum.value == pathway_type:
                return pathway_enum
        
        raise ValidationError(
            f"无效的通路类型，期望: {[p.value for p in PathwayType]}，得到: {pathway_type}",
            field_name,
            pathway_type
        )
    
    raise ValidationError(
        f"通路类型必须是字符串、PathwayType枚举或None，得到: {type(pathway_type).__name__}",
        field_name,
        pathway_type
    )


def validate_cancer_stage(
    cancer_stage: Union[str, CancerStage, None], 
    field_name: str = "cancer_stage"
) -> Optional[CancerStage]:
    """
    验证癌症分期
    
    Args:
        cancer_stage: 癌症分期值
        field_name: 字段名称
        
    Returns:
        验证后的癌症分期枚举或None
        
    Raises:
        ValidationError: 癌症分期无效时
    """
    if cancer_stage is None:
        return None
    
    if isinstance(cancer_stage, CancerStage):
        return cancer_stage
    
    if isinstance(cancer_stage, str):
        for stage_enum in CancerStage:
            if stage_enum.value == cancer_stage:
                return stage_enum
        
        raise ValidationError(
            f"无效的癌症分期，期望: {[s.value for s in CancerStage]}，得到: {cancer_stage}",
            field_name,
            cancer_stage
        )
    
    raise ValidationError(
        f"癌症分期必须是字符串、CancerStage枚举或None，得到: {type(cancer_stage).__name__}",
        field_name,
        cancer_stage
    )


def validate_state_transition(
    from_state: DiseaseState, 
    to_state: DiseaseState,
    pathway_type: Optional[PathwayType] = None
) -> bool:
    """
    验证疾病状态转换是否有效
    
    Args:
        from_state: 当前状态
        to_state: 目标状态
        pathway_type: 疾病通路类型
        
    Returns:
        转换是否有效
        
    Raises:
        StateTransitionError: 状态转换无效时
    """
    # 死亡状态不能转换到其他状态
    if from_state.is_death():
        raise StateTransitionError(
            f"死亡状态({from_state.value})不能转换到其他状态({to_state.value})"
        )
    
    # 检查通路兼容性
    if pathway_type:
        compatible_states = pathway_type.get_compatible_states()
        if to_state not in compatible_states:
            raise StateTransitionError(
                f"状态{to_state.value}与通路类型{pathway_type.value}不兼容"
            )
    
    # 定义有效的状态转换规则
    valid_transitions = {
        DiseaseState.NORMAL: {
            DiseaseState.LOW_RISK_ADENOMA,
            DiseaseState.SMALL_SERRATED,
            DiseaseState.DEATH_OTHER,
        },
        DiseaseState.LOW_RISK_ADENOMA: {
            DiseaseState.HIGH_RISK_ADENOMA,
            DiseaseState.NORMAL,
            DiseaseState.DEATH_OTHER,
        },
        DiseaseState.HIGH_RISK_ADENOMA: {
            DiseaseState.PRECLINICAL_CANCER,
            DiseaseState.NORMAL,
            DiseaseState.DEATH_OTHER,
        },
        DiseaseState.SMALL_SERRATED: {
            DiseaseState.LARGE_SERRATED,
            DiseaseState.NORMAL,
            DiseaseState.DEATH_OTHER,
        },
        DiseaseState.LARGE_SERRATED: {
            DiseaseState.PRECLINICAL_CANCER,
            DiseaseState.NORMAL,
            DiseaseState.DEATH_OTHER,
        },
        DiseaseState.PRECLINICAL_CANCER: {
            DiseaseState.CLINICAL_CANCER,
            DiseaseState.DEATH_OTHER,
        },
        DiseaseState.CLINICAL_CANCER: {
            DiseaseState.DEATH_CANCER,
            DiseaseState.DEATH_OTHER,
            DiseaseState.NORMAL,
        },
    }
    
    allowed_transitions = valid_transitions.get(from_state, set())
    if to_state not in allowed_transitions:
        raise StateTransitionError(
            f"无效的状态转换: {from_state.value} -> {to_state.value}"
        )
    
    return True


def validate_probability(
    probability: Union[int, float], 
    field_name: str = "probability"
) -> float:
    """
    验证概率值
    
    Args:
        probability: 概率值
        field_name: 字段名称
        
    Returns:
        验证后的概率值
        
    Raises:
        ParameterValidationError: 概率值无效时
    """
    try:
        prob_float = float(probability)
    except (TypeError, ValueError):
        raise ParameterValidationError(
            f"概率必须是数字类型，得到: {type(probability).__name__}",
            field_name,
            probability
        )
    
    if not (0.0 <= prob_float <= 1.0):
        raise ParameterValidationError(
            f"概率必须在0-1之间，得到: {prob_float}",
            field_name,
            prob_float
        )
    
    return prob_float


def validate_positive_number(
    number: Union[int, float], 
    field_name: str = "number",
    allow_zero: bool = False
) -> float:
    """
    验证正数
    
    Args:
        number: 数值
        field_name: 字段名称
        allow_zero: 是否允许零
        
    Returns:
        验证后的数值
        
    Raises:
        ParameterValidationError: 数值无效时
    """
    try:
        num_float = float(number)
    except (TypeError, ValueError):
        raise ParameterValidationError(
            f"数值必须是数字类型，得到: {type(number).__name__}",
            field_name,
            number
        )
    
    if allow_zero:
        if num_float < 0:
            raise ParameterValidationError(
                f"数值不能为负数，得到: {num_float}",
                field_name,
                num_float
            )
    else:
        if num_float <= 0:
            raise ParameterValidationError(
                f"数值必须大于0，得到: {num_float}",
                field_name,
                num_float
            )
    
    return num_float


def validate_individual_id(individual_id: str, field_name: str = "individual_id") -> str:
    """
    验证个体ID
    
    Args:
        individual_id: 个体ID
        field_name: 字段名称
        
    Returns:
        验证后的个体ID
        
    Raises:
        ValidationError: 个体ID无效时
    """
    if not isinstance(individual_id, str):
        raise ValidationError(
            f"个体ID必须是字符串类型，得到: {type(individual_id).__name__}",
            field_name,
            individual_id
        )
    
    if not individual_id.strip():
        raise ValidationError(
            "个体ID不能为空",
            field_name,
            individual_id
        )
    
    # 检查UUID格式（可选）
    uuid_pattern = re.compile(
        r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
        re.IGNORECASE
    )
    
    if not uuid_pattern.match(individual_id.strip()):
        # 如果不是UUID格式，至少检查基本要求
        if len(individual_id.strip()) < 3:
            raise ValidationError(
                "个体ID长度至少为3个字符",
                field_name,
                individual_id
            )
    
    return individual_id.strip()


def validate_enum_value(
    value: Any, 
    enum_class: Type[Enum], 
    field_name: str = "enum_value"
) -> Enum:
    """
    验证枚举值
    
    Args:
        value: 枚举值
        enum_class: 枚举类
        field_name: 字段名称
        
    Returns:
        验证后的枚举值
        
    Raises:
        ValidationError: 枚举值无效时
    """
    if isinstance(value, enum_class):
        return value
    
    if isinstance(value, str):
        for enum_value in enum_class:
            if enum_value.value == value:
                return enum_value
        
        raise ValidationError(
            f"无效的{enum_class.__name__}值，期望: {[e.value for e in enum_class]}，得到: {value}",
            field_name,
            value
        )
    
    raise ValidationError(
        f"{enum_class.__name__}必须是字符串或{enum_class.__name__}枚举，得到: {type(value).__name__}",
        field_name,
        value
    )


# 风险因素验证函数

def validate_bmi(bmi: Union[int, float], field_name: str = "bmi") -> float:
    """
    验证BMI值

    Args:
        bmi: BMI值
        field_name: 字段名称

    Returns:
        验证后的BMI值

    Raises:
        RiskFactorValidationError: BMI值无效时
    """
    try:
        bmi_float = float(bmi)
    except (TypeError, ValueError):
        raise RiskFactorValidationError(
            f"BMI必须是数字类型，得到: {type(bmi).__name__}",
            field_name,
            bmi
        )

    if not (10.0 <= bmi_float <= 60.0):
        raise RiskFactorValidationError(
            f"BMI值必须在10.0-60.0范围内，得到: {bmi_float}",
            field_name,
            bmi_float
        )

    return bmi_float


def validate_sedentary_hours(hours: Union[int, float], field_name: str = "sedentary_hours") -> float:
    """
    验证久坐时间

    Args:
        hours: 久坐时间（小时）
        field_name: 字段名称

    Returns:
        验证后的久坐时间

    Raises:
        RiskFactorValidationError: 久坐时间无效时
    """
    try:
        hours_float = float(hours)
    except (TypeError, ValueError):
        raise RiskFactorValidationError(
            f"久坐时间必须是数字类型，得到: {type(hours).__name__}",
            field_name,
            hours
        )

    if not (0.0 <= hours_float <= 24.0):
        raise RiskFactorValidationError(
            f"久坐时间必须在0-24小时范围内，得到: {hours_float}",
            field_name,
            hours_float
        )

    return hours_float


def validate_alcohol_consumption(units: Union[int, float], field_name: str = "alcohol_units") -> float:
    """
    验证酒精消费量

    Args:
        units: 酒精消费量（单位/周）
        field_name: 字段名称

    Returns:
        验证后的酒精消费量

    Raises:
        RiskFactorValidationError: 酒精消费量无效时
    """
    try:
        units_float = float(units)
    except (TypeError, ValueError):
        raise RiskFactorValidationError(
            f"酒精消费量必须是数字类型，得到: {type(units).__name__}",
            field_name,
            units
        )

    if not (0.0 <= units_float <= 100.0):
        raise RiskFactorValidationError(
            f"酒精消费量必须在0-100单位/周范围内，得到: {units_float}",
            field_name,
            units_float
        )

    return units_float


def validate_diet_quality_score(score: Union[int, float], field_name: str = "diet_score") -> float:
    """
    验证饮食质量评分

    Args:
        score: 饮食质量评分
        field_name: 字段名称

    Returns:
        验证后的饮食质量评分

    Raises:
        RiskFactorValidationError: 饮食质量评分无效时
    """
    try:
        score_float = float(score)
    except (TypeError, ValueError):
        raise RiskFactorValidationError(
            f"饮食质量评分必须是数字类型，得到: {type(score).__name__}",
            field_name,
            score
        )

    if not (0.0 <= score_float <= 100.0):
        raise RiskFactorValidationError(
            f"饮食质量评分必须在0-100范围内，得到: {score_float}",
            field_name,
            score_float
        )

    return score_float


def validate_boolean_risk_factor(value: Any, field_name: str = "boolean_factor") -> bool:
    """
    验证布尔型风险因素

    Args:
        value: 布尔值
        field_name: 字段名称

    Returns:
        验证后的布尔值

    Raises:
        RiskFactorValidationError: 布尔值无效时
    """
    if isinstance(value, bool):
        return value

    if isinstance(value, str):
        value_lower = value.lower().strip()
        if value_lower in ['true', '1', 'yes', 'y', '是', '有']:
            return True
        elif value_lower in ['false', '0', 'no', 'n', '否', '无']:
            return False
        else:
            raise RiskFactorValidationError(
                f"无效的布尔值，期望: true/false, yes/no, 1/0，得到: {value}",
                field_name,
                value
            )

    if isinstance(value, (int, float)):
        if value == 1 or value == 1.0:
            return True
        elif value == 0 or value == 0.0:
            return False
        else:
            raise RiskFactorValidationError(
                f"数字类型的布尔值只能是0或1，得到: {value}",
                field_name,
                value
            )

    raise RiskFactorValidationError(
        f"布尔型风险因素必须是布尔、字符串或数字类型，得到: {type(value).__name__}",
        field_name,
        value
    )


def validate_risk_factor_value(factor_type, value: Any, field_name: str = "risk_factor_value") -> Any:
    """
    根据风险因素类型验证值

    Args:
        factor_type: 风险因素类型枚举
        value: 风险因素值
        field_name: 字段名称

    Returns:
        验证后的风险因素值

    Raises:
        RiskFactorValidationError: 风险因素值无效时
    """
    # 动态导入避免循环依赖
    try:
        from ..modules.disease.risk_factors import RiskFactorType
    except ImportError:
        raise RiskFactorValidationError(
            "无法导入RiskFactorType，请检查模块依赖",
            field_name,
            value
        )

    if not isinstance(factor_type, RiskFactorType):
        raise RiskFactorValidationError(
            f"factor_type必须是RiskFactorType枚举，得到: {type(factor_type).__name__}",
            field_name,
            factor_type
        )

    # 根据风险因素类型进行特定验证
    if factor_type == RiskFactorType.BMI:
        return validate_bmi(value, field_name)
    elif factor_type == RiskFactorType.SEDENTARY_LIFESTYLE:
        return validate_sedentary_hours(value, field_name)
    elif factor_type == RiskFactorType.ALCOHOL_CONSUMPTION:
        return validate_alcohol_consumption(value, field_name)
    elif factor_type == RiskFactorType.DIET_QUALITY:
        return validate_diet_quality_score(value, field_name)
    elif factor_type.is_boolean():
        return validate_boolean_risk_factor(value, field_name)
    else:
        # 对于其他连续型风险因素，进行基本数值验证
        if factor_type.is_continuous():
            return validate_positive_number(value, field_name, allow_zero=True)
        else:
            raise RiskFactorValidationError(
                f"未知的风险因素类型: {factor_type.value}",
                field_name,
                factor_type
            )


def validate_risk_factor_weight(weight: Union[int, float], field_name: str = "weight") -> float:
    """
    验证风险因素权重

    Args:
        weight: 权重值
        field_name: 字段名称

    Returns:
        验证后的权重值

    Raises:
        RiskFactorValidationError: 权重值无效时
    """
    try:
        weight_float = float(weight)
    except (TypeError, ValueError):
        raise RiskFactorValidationError(
            f"权重必须是数字类型，得到: {type(weight).__name__}",
            field_name,
            weight
        )

    if weight_float < 0:
        raise RiskFactorValidationError(
            f"权重不能为负数，得到: {weight_float}",
            field_name,
            weight_float
        )

    if weight_float > 10.0:
        raise RiskFactorValidationError(
            f"权重不能超过10.0，得到: {weight_float}",
            field_name,
            weight_float
        )

    return weight_float


def validate_risk_factor_profile_completeness(risk_profile, required_factors: Optional[List] = None) -> Dict[str, Any]:
    """
    验证风险因素档案的完整性

    Args:
        risk_profile: 风险因素档案
        required_factors: 必需的风险因素列表

    Returns:
        验证结果字典

    Raises:
        RiskFactorValidationError: 档案不完整时
    """
    try:
        from ..modules.disease.risk_factors import RiskFactorType, RiskFactorProfile
    except ImportError:
        raise RiskFactorValidationError(
            "无法导入风险因素模块，请检查模块依赖"
        )

    if not isinstance(risk_profile, RiskFactorProfile):
        raise RiskFactorValidationError(
            f"risk_profile必须是RiskFactorProfile类型，得到: {type(risk_profile).__name__}"
        )

    # 默认必需的风险因素
    if required_factors is None:
        required_factors = [
            RiskFactorType.FAMILY_HISTORY,
            RiskFactorType.BMI
        ]

    missing_factors = []
    invalid_factors = []

    for factor_type in required_factors:
        if not risk_profile.has_risk_factor(factor_type):
            missing_factors.append(factor_type.value)
        else:
            # 验证现有风险因素的值
            risk_factor = risk_profile.get_risk_factor(factor_type)
            try:
                validate_risk_factor_value(factor_type, risk_factor.value)
            except RiskFactorValidationError:
                invalid_factors.append(factor_type.value)

    validation_result = {
        "is_complete": len(missing_factors) == 0 and len(invalid_factors) == 0,
        "missing_factors": missing_factors,
        "invalid_factors": invalid_factors,
        "total_factors": len(risk_profile.risk_factors),
        "required_factors_count": len(required_factors)
    }

    if not validation_result["is_complete"]:
        error_messages = []
        if missing_factors:
            error_messages.append(f"缺少必需的风险因素: {missing_factors}")
        if invalid_factors:
            error_messages.append(f"风险因素值无效: {invalid_factors}")

        raise RiskFactorValidationError(
            "; ".join(error_messages),
            "risk_profile_completeness",
            validation_result
        )

    return validation_result


def validate_age_risk_factor_consistency(age: float, risk_factors: Dict) -> bool:
    """
    验证年龄与风险因素的一致性

    Args:
        age: 年龄
        risk_factors: 风险因素字典

    Returns:
        是否一致

    Raises:
        RiskFactorValidationError: 不一致时
    """
    warnings = []

    # 年龄相关的一致性检查
    if age < 18:
        # 未成年人不应该有某些风险因素
        adult_factors = ['smoking_status', 'alcohol_consumption']
        for factor_name in adult_factors:
            if factor_name in risk_factors:
                warnings.append(f"未成年人({age}岁)不应该有{factor_name}风险因素")

    if age > 80:
        # 高龄人群的BMI范围可能需要调整
        if 'body_mass_index' in risk_factors:
            bmi = risk_factors['body_mass_index']
            if isinstance(bmi, (int, float)) and bmi < 18.5:
                warnings.append(f"高龄人群({age}岁)BMI过低({bmi})可能存在健康风险")

    if warnings:
        raise RiskFactorValidationError(
            f"年龄与风险因素不一致: {'; '.join(warnings)}",
            "age_risk_consistency",
            {"age": age, "warnings": warnings}
        )

    return True


# 筛查工具验证函数

class ScreeningToolValidationError(ValidationError):
    """筛查工具验证错误"""
    pass


def validate_sensitivity(
    sensitivity: Union[int, float],
    field_name: str = "sensitivity"
) -> float:
    """
    验证敏感性值

    Args:
        sensitivity: 敏感性值
        field_name: 字段名称

    Returns:
        验证后的敏感性值

    Raises:
        ScreeningToolValidationError: 敏感性值无效时
    """
    try:
        sens_float = float(sensitivity)
    except (TypeError, ValueError):
        raise ScreeningToolValidationError(
            f"敏感性必须是数字类型，得到: {type(sensitivity).__name__}",
            field_name,
            sensitivity
        )

    if not (0.0 <= sens_float <= 1.0):
        raise ScreeningToolValidationError(
            f"敏感性必须在0-1之间，得到: {sens_float}",
            field_name,
            sens_float
        )

    return sens_float


def validate_specificity(
    specificity: Union[int, float],
    field_name: str = "specificity"
) -> float:
    """
    验证特异性值

    Args:
        specificity: 特异性值
        field_name: 字段名称

    Returns:
        验证后的特异性值

    Raises:
        ScreeningToolValidationError: 特异性值无效时
    """
    try:
        spec_float = float(specificity)
    except (TypeError, ValueError):
        raise ScreeningToolValidationError(
            f"特异性必须是数字类型，得到: {type(specificity).__name__}",
            field_name,
            specificity
        )

    if not (0.0 <= spec_float <= 1.0):
        raise ScreeningToolValidationError(
            f"特异性必须在0-1之间，得到: {spec_float}",
            field_name,
            spec_float
        )

    return spec_float


def validate_cost_value(
    cost: Union[int, float],
    field_name: str = "cost",
    max_cost: float = 100000.0
) -> float:
    """
    验证成本值

    Args:
        cost: 成本值
        field_name: 字段名称
        max_cost: 最大允许成本

    Returns:
        验证后的成本值

    Raises:
        ScreeningToolValidationError: 成本值无效时
    """
    try:
        cost_float = float(cost)
    except (TypeError, ValueError):
        raise ScreeningToolValidationError(
            f"成本必须是数字类型，得到: {type(cost).__name__}",
            field_name,
            cost
        )

    if cost_float < 0:
        raise ScreeningToolValidationError(
            f"成本不能为负数，得到: {cost_float}",
            field_name,
            cost_float
        )

    if cost_float > max_cost:
        raise ScreeningToolValidationError(
            f"成本不能超过{max_cost}，得到: {cost_float}",
            field_name,
            cost_float
        )

    return cost_float


def validate_detection_threshold(
    threshold: Union[int, float, None],
    field_name: str = "detection_threshold"
) -> Optional[float]:
    """
    验证检测阈值

    Args:
        threshold: 检测阈值
        field_name: 字段名称

    Returns:
        验证后的检测阈值

    Raises:
        ScreeningToolValidationError: 检测阈值无效时
    """
    if threshold is None:
        return None

    try:
        threshold_float = float(threshold)
    except (TypeError, ValueError):
        raise ScreeningToolValidationError(
            f"检测阈值必须是数字类型或None，得到: {type(threshold).__name__}",
            field_name,
            threshold
        )

    if threshold_float <= 0:
        raise ScreeningToolValidationError(
            f"检测阈值必须大于0，得到: {threshold_float}",
            field_name,
            threshold_float
        )

    return threshold_float


def validate_operator_dependency(
    dependency: Union[int, float],
    field_name: str = "operator_dependency"
) -> float:
    """
    验证操作者依赖性因子

    Args:
        dependency: 操作者依赖性因子
        field_name: 字段名称

    Returns:
        验证后的操作者依赖性因子

    Raises:
        ScreeningToolValidationError: 操作者依赖性因子无效时
    """
    try:
        dep_float = float(dependency)
    except (TypeError, ValueError):
        raise ScreeningToolValidationError(
            f"操作者依赖性因子必须是数字类型，得到: {type(dependency).__name__}",
            field_name,
            dependency
        )

    if not (0.1 <= dep_float <= 5.0):
        raise ScreeningToolValidationError(
            f"操作者依赖性因子必须在0.1-5.0之间，得到: {dep_float}",
            field_name,
            dep_float
        )

    return dep_float


def validate_turnaround_time(
    time_days: Union[int, float],
    field_name: str = "turnaround_time_days"
) -> int:
    """
    验证周转时间

    Args:
        time_days: 周转时间（天）
        field_name: 字段名称

    Returns:
        验证后的周转时间

    Raises:
        ScreeningToolValidationError: 周转时间无效时
    """
    try:
        time_int = int(time_days)
    except (TypeError, ValueError):
        raise ScreeningToolValidationError(
            f"周转时间必须是整数类型，得到: {type(time_days).__name__}",
            field_name,
            time_days
        )

    if not (0 <= time_int <= 30):
        raise ScreeningToolValidationError(
            f"周转时间必须在0-30天之间，得到: {time_int}",
            field_name,
            time_int
        )

    return time_int


def validate_screening_tool_configuration(
    tool_config: Dict[str, Any],
    tool_type: Optional[Any] = None
) -> Dict[str, Any]:
    """
    验证筛查工具配置的完整性

    Args:
        tool_config: 工具配置字典
        tool_type: 工具类型（可选）

    Returns:
        验证后的配置字典

    Raises:
        ScreeningToolValidationError: 配置无效时
    """
    if not isinstance(tool_config, dict):
        raise ScreeningToolValidationError(
            f"工具配置必须是字典类型，得到: {type(tool_config).__name__}",
            "tool_config",
            tool_config
        )

    validated_config = {}

    # 验证性能参数
    if "sensitivity_by_state" in tool_config:
        sensitivity_config = tool_config["sensitivity_by_state"]
        if not isinstance(sensitivity_config, dict):
            raise ScreeningToolValidationError(
                "sensitivity_by_state必须是字典类型",
                "sensitivity_by_state",
                sensitivity_config
            )

        validated_sensitivity = {}
        for state, sensitivity in sensitivity_config.items():
            validated_sensitivity[state] = validate_sensitivity(
                sensitivity, f"sensitivity_by_state[{state}]"
            )
        validated_config["sensitivity_by_state"] = validated_sensitivity

    # 验证特异性
    if "specificity" in tool_config:
        validated_config["specificity"] = validate_specificity(
            tool_config["specificity"]
        )

    # 验证检测阈值
    if "detection_threshold" in tool_config:
        validated_config["detection_threshold"] = validate_detection_threshold(
            tool_config["detection_threshold"]
        )

    # 验证操作者依赖性
    if "operator_dependency" in tool_config:
        validated_config["operator_dependency"] = validate_operator_dependency(
            tool_config["operator_dependency"]
        )

    # 验证周转时间
    if "turnaround_time_days" in tool_config:
        validated_config["turnaround_time_days"] = validate_turnaround_time(
            tool_config["turnaround_time_days"]
        )

    # 验证成本配置
    if "costs" in tool_config:
        cost_config = tool_config["costs"]
        if not isinstance(cost_config, dict):
            raise ScreeningToolValidationError(
                "costs必须是字典类型",
                "costs",
                cost_config
            )

        validated_costs = {}
        for cost_type, cost_value in cost_config.items():
            validated_costs[cost_type] = validate_cost_value(
                cost_value, f"costs[{cost_type}]"
            )
        validated_config["costs"] = validated_costs

    # 复制其他配置项
    for key, value in tool_config.items():
        if key not in validated_config:
            validated_config[key] = value

    return validated_config


def validate_performance_parameter_consistency(
    sensitivity_by_state: Dict[str, float],
    specificity: float
) -> bool:
    """
    验证性能参数的逻辑一致性

    Args:
        sensitivity_by_state: 疾病状态特异性敏感性
        specificity: 特异性

    Returns:
        是否一致

    Raises:
        ScreeningToolValidationError: 参数不一致时
    """
    warnings = []

    # 检查敏感性的逻辑顺序
    # 一般来说，癌症的敏感性应该高于腺瘤
    cancer_sensitivity = sensitivity_by_state.get("clinical_cancer_stage_i", 0.0)
    adenoma_sensitivity = sensitivity_by_state.get("high_risk_adenoma", 0.0)

    if cancer_sensitivity < adenoma_sensitivity:
        warnings.append(
            f"癌症敏感性({cancer_sensitivity})低于高风险腺瘤敏感性({adenoma_sensitivity})，这可能不合理"
        )

    # 检查特异性是否过低
    if specificity < 0.8:
        warnings.append(
            f"特异性({specificity})过低，可能导致过多假阳性"
        )

    # 检查敏感性是否过高（可能不现实）
    max_sensitivity = max(sensitivity_by_state.values()) if sensitivity_by_state else 0.0
    if max_sensitivity > 1.0:
        warnings.append(
            f"最大敏感性({max_sensitivity})超过1.0，这是不可能的"
        )

    if warnings:
        raise ScreeningToolValidationError(
            f"性能参数不一致: {'; '.join(warnings)}",
            "performance_consistency",
            {"sensitivity_by_state": sensitivity_by_state, "specificity": specificity}
        )

    return True


def validate_cost_parameter_reasonableness(
    cost_config: Dict[str, float],
    tool_type: Optional[str] = None
) -> bool:
    """
    验证成本参数的合理性

    Args:
        cost_config: 成本配置字典
        tool_type: 工具类型

    Returns:
        是否合理

    Raises:
        ScreeningToolValidationError: 成本参数不合理时
    """
    warnings = []

    # 检查成本组成的合理性
    total_cost = sum(cost_config.values())

    # 基于工具类型的成本范围检查
    if tool_type:
        expected_ranges = {
            "fit": (10, 500),           # FIT成本范围（放宽）
            "fecal_immunochemical_test": (10, 500),  # FIT成本范围（别名）
            "colonoscopy": (200, 5000), # 结肠镜成本范围（放宽）
            "sigmoidoscopy": (100, 2000), # 乙状结肠镜成本范围（放宽）
            "flexible_sigmoidoscopy": (100, 2000)  # 乙状结肠镜成本范围（别名）
        }

        if tool_type.lower() in expected_ranges:
            min_cost, max_cost = expected_ranges[tool_type.lower()]
            if total_cost < min_cost:
                warnings.append(
                    f"{tool_type}总成本({total_cost})低于预期范围({min_cost}-{max_cost})"
                )
            elif total_cost > max_cost:
                warnings.append(
                    f"{tool_type}总成本({total_cost})高于预期范围({min_cost}-{max_cost})"
                )

    # 检查成本组成比例
    if "direct_cost" in cost_config and "indirect_cost" in cost_config:
        direct_cost = cost_config["direct_cost"]
        indirect_cost = cost_config["indirect_cost"]

        if indirect_cost > direct_cost * 2:
            warnings.append(
                f"间接成本({indirect_cost})过高，超过直接成本({direct_cost})的2倍"
            )

    if warnings:
        raise ScreeningToolValidationError(
            f"成本参数不合理: {'; '.join(warnings)}",
            "cost_reasonableness",
            cost_config
        )

    return True
